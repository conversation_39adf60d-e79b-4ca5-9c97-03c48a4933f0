// Base card styles
.card {
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;

  &.hoverable {
    cursor: pointer;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }
  }

  &.collapsible {
    .header {
      cursor: pointer;
      user-select: none;

      &:hover {
        background-color: rgba(0, 0, 0, 0.02);
      }
    }
  }

  &.collapsed {
    .content {
      display: none;
    }
  }
}

// Header styles
.header {
  padding: 12px 16px;
  font-size: 16px;
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.headerTop {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #262626;
  transition: all 0.3s ease;

  &.noBorder {
    border-bottom: none;
  }
}

.headerContent {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.description {
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1.4;
  margin-top: 8px;
}

.headerIcon {
  display: flex;
  align-items: center;
  font-size: 16px;
}

.headerTitle {
  flex: 1;
  margin: 0;
}

.headerExtra {
  display: flex;
  align-items: center;
  gap: 8px;
}

.collapseIcon {
  transition: transform 0.3s ease;
}

// Content styles
.content {
  background-color: #ffffff;
  transition: all 0.3s ease;
  padding: 16px;
}

// Border style variants
.borderSolid {
  border-style: solid;
}

.borderDashed {
  border-style: dashed;
}

.borderDotted {
  border-style: dotted;
}

.borderNone {
  border: none;
}

// Default theme
.card {
  background-color: #ffffff;
  border-color: #d9d9d9;
  color: #262626;
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .card {
    background-color: #1f1f1f;
    border-color: #434343;
    color: #ffffff;

    .header {
      background-color: #262626;
      color: #ffffff;
    }

    .content {
      background-color: #1f1f1f;
      color: #ffffff;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .card {
    &.large {
      .header {
        padding: 12px 16px;
        font-size: 16px;
      }

      .content {
        padding: 16px;
      }
    }
  }

  .headerContent {
    gap: 4px;
  }

  .headerExtra {
    gap: 4px;
  }
}
