import type { ReactNode } from 'react';

export type CardSize = 'small' | 'default' | 'large';

// Header configuration
export interface CardHeaderConfig {
  title?: ReactNode;
  extra?: ReactNode;
  icon?: ReactNode;
  description?: ReactNode;
  backgroundColor?: string;
  textColor?: string;
  borderBottom?: boolean;
  padding?: string | number;
}

export interface CardContentConfig {
  padding?: string | number;
  backgroundColor?: string;
  minHeight?: string | number;
}

export interface CardConfig {
  size?: CardSize;
  header?: CardHeaderConfig;
  description?: CardHeaderConfig;
  content?: CardContentConfig;
  hoverable?: boolean;
  collapsible?: boolean;
  defaultCollapsed?: boolean;
  className?: string;
  style?: React.CSSProperties;
  role?: string;
}

// Props for the ConfigurableCard component
export interface ConfigurableCardProps extends CardConfig {
  children: ReactNode;
  onCollapse?: (collapsed: boolean) => void;
  onClick?: (event: React.MouseEvent<HTMLDivElement>) => void;
}
