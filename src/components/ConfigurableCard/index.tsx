import {
  DownOutlined,
  RightOutlined,
} from '@ant-design/icons';
import cx from 'classnames';
import type { CSSProperties } from 'react';
import React, {
  useMemo,
  useState,
} from 'react';

import styles from './style.module.scss';
import type {
  ConfigurableCardProps,
} from './types';

const ConfigurableCard = ({
  children,
  header,
  content,
  hoverable = true,
  collapsible = false,
  defaultCollapsed = false,
  className,
  style,
  onCollapse,
  onClick,
}: ConfigurableCardProps) => {
  const [collapsed, setCollapsed] = useState(defaultCollapsed);

  // Generate CSS classes
  const cardClasses = useMemo(() => {
    const classes: string[] = [styles.card];

    if (hoverable) {
      classes.push(styles.hoverable);
    }

    if (collapsible) {
      classes.push(styles.collapsible);
    }

    if (collapsed) {
      classes.push(styles.collapsed);
    }

    // Custom className
    if (className) {
      classes.push(className);
    }

    return cx(classes);
  }, [hoverable, collapsible, collapsed, className]);

  // Generate inline styles
  const cardStyle = useMemo((): CSSProperties => {
    const computedStyle: CSSProperties = { ...style };

    // Apply content background if specified
    if (content?.backgroundColor) {
      computedStyle.backgroundColor = content.backgroundColor;
    }

    return computedStyle;
  }, [style, content?.backgroundColor]);

  // Generate header styles
  const headerStyle = useMemo((): CSSProperties => {
    const computedStyle: CSSProperties = {};

    if (header?.backgroundColor) {
      computedStyle.backgroundColor = header.backgroundColor;
    }

    if (header?.textColor) {
      computedStyle.color = header.textColor;
    }

    if (header?.padding !== undefined) {
      computedStyle.padding = typeof header.padding === 'number' ? `${ header.padding }px` : header.padding;
    }

    return computedStyle;
  }, [header]);

  // Generate content styles
  const contentStyle = useMemo((): CSSProperties => {
    const computedStyle: CSSProperties = {};

    if (content?.backgroundColor) {
      computedStyle.backgroundColor = content.backgroundColor;
    }

    if (content?.padding !== undefined) {
      computedStyle.padding = typeof content.padding === 'number' ? `${ content.padding }px` : content.padding;
    }

    if (content?.minHeight !== undefined) {
      computedStyle.minHeight = typeof content.minHeight === 'number' ? `${ content.minHeight }px` : content.minHeight;
    }

    return computedStyle;
  }, [content]);

  // Handle collapse toggle
  const handleCollapseToggle = () => {
    const newCollapsed = !collapsed;
    setCollapsed(newCollapsed);
    onCollapse?.(newCollapsed);
  };

  // Handle card click
  const handleCardClick = (event: React.MouseEvent<HTMLDivElement>) => {
    onClick?.(event);
  };

  // Render header
  const renderHeader = () => {
    if (!header?.title && !header?.extra && !header?.icon) {
      return null;
    }

    const headerClasses = cx(
      styles.header,
      header?.borderBottom === false && styles.noBorder,
    );

    return (
      <>
        <div
          className={ headerClasses }
          style={ headerStyle }
          onClick={ collapsible ? handleCollapseToggle : undefined }
        >
          <div className={styles.headerTop}>
            <div className={ styles.headerContent }>
              { header?.icon && (
                <div className={ styles.headerIcon }>
                  { header.icon }
                </div>
              ) }
              { header?.title && (
                <div className={ styles.headerTitle }>
                  { header.title }
                </div>
              ) }
            </div>
            <div className={ styles.headerExtra }>
              { header?.extra }
              { collapsible && (
                <div className={ cx(styles.collapseIcon) }>
                  { collapsed ? <RightOutlined/> : <DownOutlined/> }
                </div>
              ) }
            </div>
          </div>
          { header?.description && (
            <div className={ styles.description }>
              { header.description }
            </div>
          ) }
        </div>
      </>
    );
  };

  return (
    <div
      className={ cardClasses }
      style={ cardStyle }
      onClick={ handleCardClick }
    >
      { renderHeader() }
      <div className={ styles.content } style={ contentStyle }>
        { children }
      </div>
    </div>
  );
};

export default ConfigurableCard;
