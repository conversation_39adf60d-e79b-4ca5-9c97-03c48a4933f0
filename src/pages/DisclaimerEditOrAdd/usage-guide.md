# 使用指南

## 重构后的组件结构

### 主要组件

#### 1. DisclaimerEditOrAdd (index.tsx)
**职责**: 主要的状态管理和组件协调
- 管理表单实例
- 管理高级规则状态
- 处理 API 调用
- 协调子组件间的通信

#### 2. GeneralInformation
**职责**: 处理基本信息表单
- 表单验证
- 文件上传
- 状态切换
- 只读/编辑模式切换

#### 3. AdvancedSettings
**职责**: 处理高级规则设置
- 规则的增删改查
- 验证错误显示
- 分类和属性管理

#### 4. RuleSummary
**职责**: 显示规则摘要
- 生成规则描述
- 格式化显示

## 表单验证使用

### 验证规则配置
```typescript
// 在 GeneralInformation.tsx 中定义
export const validationRules = {
  disclaimerName: [
    { required: true, message: 'Please enter disclaimer name' },
    { max: 100, message: 'Disclaimer name cannot exceed 100 characters' },
    { whitespace: true, message: 'Disclaimer name cannot be empty' },
  ],
  disclaimerDescription: [
    { required: true, message: 'Please enter disclaimer description' },
    { max: 1000, message: 'Description cannot exceed 1000 characters' },
    { whitespace: true, message: 'Description cannot be empty' },
  ],
};
```

### 表单提交验证
```typescript
// 在主组件中
const handleSaveGeneralInfo = async () => {
  try {
    // 验证指定字段
    await form.validateFields(['name', 'description']);
    // 处理保存逻辑
  } catch (error) {
    if (error.errorFields) {
      message.error('Please check the form fields and try again.');
    }
  }
};
```

## 组件通信模式

### Props 传递
```typescript
// 主组件向子组件传递状态和回调
<GeneralInformation
  form={form}
  currentStep={currentStep}
  isEdit={isEdit}
  loading={loading}
  statusValue={statusValue}
  onSave={handleSaveGeneralInfo}
  onCancel={handleCancel}
  onFileUpload={handleFileUpload}
/>
```

### 回调函数
```typescript
// 子组件通过回调函数通知主组件
interface GeneralInformationProps {
  onSave: () => void;
  onCancel: () => void;
  onFileUpload: (data?: { hash?: string; filename?: string }) => void;
}
```

## 开发建议

### 1. 添加新字段
如果需要在 General Information 中添加新字段：
1. 在 `GeneralInformation.tsx` 中添加 FormField
2. 在 `validationRules` 中添加验证规则
3. 更新表单的 `initialValues`

### 2. 修改高级规则
如果需要修改高级规则逻辑：
1. 主要在 `AdvancedSettings.tsx` 中修改
2. 如果需要新的状态，在主组件中添加并通过 props 传递

### 3. 样式修改
- 样式文件仍然是 `style.module.scss`
- 各组件共享同一个样式文件
- 保持样式的一致性

### 4. 测试
每个组件现在可以独立测试：
```typescript
// 测试 GeneralInformation
import { render, screen } from '@testing-library/react';
import GeneralInformation from './GeneralInformation';

test('should validate required fields', () => {
  // 测试验证逻辑
});

// 测试 AdvancedSettings
test('should add new rule', () => {
  // 测试规则添加
});
```

## 性能优化

### 1. 组件懒加载
```typescript
import { lazy, Suspense } from 'react';

const AdvancedSettings = lazy(() => import('./components/AdvancedSettings'));

// 在 render 中
<Suspense fallback={<div>Loading...</div>}>
  <AdvancedSettings {...props} />
</Suspense>
```

### 2. 回调函数优化
```typescript
// 使用 useCallback 优化回调函数
const handleSaveGeneralInfo = useCallback(async () => {
  // 保存逻辑
}, [form, isEdit]);
```

### 3. 状态优化
```typescript
// 使用 useMemo 优化计算
const usedCategoryIds = useMemo(() => {
  return advancedRules.map(rule => rule.catId!).filter(Boolean);
}, [advancedRules]);
```

## 故障排除

### 常见问题

1. **表单验证不工作**
   - 检查 Form.Item 的 name 属性
   - 确认 rules 属性正确传递

2. **组件间通信失败**
   - 检查 props 传递是否正确
   - 确认回调函数的签名匹配

3. **样式问题**
   - 确认 className 引用正确
   - 检查 CSS 模块导入

4. **状态更新问题**
   - 确认使用正确的状态更新函数
   - 检查依赖数组是否正确
