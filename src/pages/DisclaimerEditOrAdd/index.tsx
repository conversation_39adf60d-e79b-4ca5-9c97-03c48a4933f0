import {
  CloseOutlined,
  DeleteOutlined,
  PlusOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { getCountry } from '@classification/admin-solution';
import {
  Button,
  Form,
  Input,
  message,
  Popconfirm,
  Switch,
  Typography,
} from 'antd';
import React, {
  useEffect,
  useState,
} from 'react';
import {
  useNavigate,
  useSearchParams,
} from 'react-router-dom';

import type { OperationType } from 'src/api/disclaimer/constants';
import type {
  disclaimer,
} from 'src/api/disclaimer/disclaimer';
import {
  getAttrList,
} from 'src/api/globalAttribute';
import type {
  globalAttribute,
} from 'src/api/globalAttribute/globalAttribute';
import { getGlobalCategoryList } from 'src/api/uploadAdmin';
import type {
  uploadAdmin,
} from 'src/api/uploadAdmin/uploadAdmin';
import ConfigurableCard from 'src/components/ConfigurableCard';
import UploadFile from 'src/components/Upload';
import { DisclaimerOperationMap } from 'src/constants/disclaimer';
import AttributeRuleTable from './components/AttributeRuleTable';
import RuleModal from './components/RuleModal';
import style from './style.module.scss';

const { TextArea } = Input;
const { Title, Text } = Typography;

// 表单验证规则
const validationRules = {
  disclaimerName: [
    { required: true, message: 'Please enter disclaimer name' },
    { max: 100, message: 'Disclaimer name cannot exceed 100 characters' },
    { whitespace: true, message: 'Disclaimer name cannot be empty' },
  ],
  disclaimerDescription: [
    { required: true, message: 'Please enter disclaimer description' },
    { max: 1000, message: 'Description cannot exceed 1000 characters' },
    { whitespace: true, message: 'Description cannot be empty' },
  ],
};

// 可复用的表单字段组件 - 现在使用 Form.Item
interface FormFieldProps {
  label: string;
  name: string;
  required?: boolean;
  rules?: any[];
  children: React.ReactNode;
}

const FormField: React.FC<FormFieldProps> = ({ label, name, required = false, rules = [], children }) => (
  <div className={ style.formField }>
    <Text strong>
      { label } { required && '*' }
    </Text>
    <div style={ { marginTop: 8 } }>
      <Form.Item name={name} rules={rules} style={{ marginBottom: 0 }}>
        { children }
      </Form.Item>
    </div>
  </div>
);

// 可复用的规则卡片组件
interface RuleCardProps {
  rule: disclaimer.IUpdateDisclaimerConditionRequest;
  index: number;
  allRules: disclaimer.IUpdateDisclaimerConditionRequest[]; // 添加所有规则的引用
  availableCategories: uploadAdmin.ICategory[];
  categoriesMap: Map<number, uploadAdmin.ICategory>;
  categoryAttributes: Map<number, globalAttribute.IGlobalAttr[]>;
  categoriesLoading: boolean;
  onUpdate: (conditionId: number, index: number, updates: Partial<disclaimer.IUpdateDisclaimerConditionRequest>) => void;
  onDelete: (conditionId: number,index: number) => void;
  onCategoryChange: (categoryId: number) => void;
  onEditRule: (rule: disclaimer.IUpdateDisclaimerConditionRequest) => void;
}

const RuleCard: React.FC<RuleCardProps> = ({
  rule,
  index,
  allRules,
  availableCategories,
  categoryAttributes,
  categoriesMap,
  categoriesLoading,
  onUpdate,
  onDelete,
  onEditRule,
}) => {

  // 处理 Attribute Rule 的增删改
  const handleAddAttributeRule = (attributeRule: disclaimer.IAttrCondition) => {
    const updatedRule = {
      ...rule,
      attrConditionInfo: {
        attrConditionList: [
          ...(rule.attrConditionInfo?.attrConditionList || []),
          attributeRule,
        ],
      },
    };
    onUpdate(rule.conditionId!, index, updatedRule);
  };

  const handleEditAttributeRule = (attrIndex: number, attributeRule: disclaimer.IAttrCondition) => {
    const updatedList = [...(rule.attrConditionInfo?.attrConditionList || [])];
    updatedList[attrIndex] = attributeRule;
    const updatedRule = {
      ...rule,
      attrConditionInfo: {
        attrConditionList: updatedList,
      },
    };
    onUpdate(rule.conditionId!, index, updatedRule);
  };

  const handleDeleteAttributeRule = (attrIndex: number) => {
    const updatedList = [...(rule.attrConditionInfo?.attrConditionList || [])];
    updatedList.splice(attrIndex, 1);
    const updatedRule = {
      ...rule,
      attrConditionInfo: {
        attrConditionList: updatedList,
      },
    };
    onUpdate(rule.conditionId!, index, updatedRule);
  };

  return (
    <ConfigurableCard
      key={ rule.conditionId || index }
      header={ {
        title: `Rule ${ index + 1 } — ${ availableCategories.find(cat => cat.catId === rule.catId)?.catName || 'Unnamed' +
        ' Category' }`,
        extra: (
          <Popconfirm
            title="Delete this rule?"
            onConfirm={ () => onDelete(rule.conditionId!, index) }
            okText="Yes"
            cancelText="No"
          >
            <Button
              type="text"
              danger
              icon={ <DeleteOutlined/> }
              className={ style.deleteButton }
            />
          </Popconfirm>
        ),
      } }
      className={ style.ruleCard }
    >

      <div className={ style.ruleContent }>
        <FormField label="Associated Category">
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <div style={{
              flex: 1,
              padding: '4px 11px',
              border: '1px solid #d9d9d9',
              borderRadius: '6px',
              backgroundColor: '#f5f5f5',
              minHeight: '32px',
              display: 'flex',
              alignItems: 'center'
            }}>
              <Text>
                {categoriesMap.get(rule.catId!)?.catName || ''}
              </Text>
            </div>
            <Button
              type="default"
              size="small"
              onClick={() => onEditRule(rule)}
              disabled={categoriesLoading}
            >
              Edit
            </Button>
          </div>
        </FormField>

        <AttributeRuleTable
          attributeRules={ rule.attrConditionInfo?.attrConditionList || [] }
          categoryAttributes={ categoryAttributes.get(rule.catId!) || [] }
          onAdd={ handleAddAttributeRule }
          onEdit={ handleEditAttributeRule }
          onDelete={ handleDeleteAttributeRule }
        />
      </div>
    </ConfigurableCard>
  );
};

const DisclaimerEditOrAdd: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const id = searchParams.get('id');
  const isEdit = !!id;

  // Ant Design Form 实例
  const [form] = Form.useForm();

  // 高级规则状态 (不在表单中，单独管理)
  const [advancedRules, setAdvancedRules] = useState<disclaimer.IUpdateDisclaimerConditionRequest[]>([]);

  // 监听表单值变化
  const statusValue = Form.useWatch('status', form);

  // UI状态
  const [currentStep, setCurrentStep] = useState<'general' | 'advanced'>(isEdit ? 'advanced' : 'general');
  const [generalInfoSaved, setGeneralInfoSaved] = useState(isEdit);
  const [advancedSettingsExpanded, setAdvancedSettingsExpanded] = useState(false);
  const [loading, setLoading] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [ruleModalVisible, setRuleModalVisible] = useState(false);
  const [ruleModalMode, setRuleModalMode] = useState<'add' | 'edit'>('add');
  const [editingRule, setEditingRule] = useState<disclaimer.IUpdateDisclaimerConditionRequest | undefined>();

  // 动态数据状态
  const [availableCategories, setAvailableCategories] = useState<uploadAdmin.ICategory[]>([]);
  const [categoriesMap, setCategoriesMap] = useState<Map<number, uploadAdmin.ICategory>>(new Map());
  const [categoryAttributes, setCategoryAttributes] = useState<Map<number, globalAttribute.IGlobalAttr[]>>(new Map());
  const [categoriesLoading, setCategoriesLoading] = useState(false);

  // 初始化数据
  useEffect(() => {
    // 加载可用分类
    loadAvailableCategories();

    if (isEdit) {
      loadDisclaimerData();
    }
  }, [id]);

  // 构建分类 Map
  const buildCategoriesMap = (categories: uploadAdmin.ICategory[]): Map<number, uploadAdmin.ICategory> => {
    const map = new Map<number, uploadAdmin.ICategory>();

    const addToMap = (cats: uploadAdmin.ICategory[]) => {
      for (const cat of cats) {
        if (cat.catId) {
          map.set(cat.catId, cat);
        }
        if (cat.subCategories) {
          addToMap(cat.subCategories);
        }
      }
    };

    addToMap(categories);
    return map;
  };

  // 加载可用分类
  const loadAvailableCategories = async () => {
    setCategoriesLoading(true);
    try {
      const response = await getGlobalCategoryList({
        region: getCountry(),
      });
      if (response?.cats) {
        setAvailableCategories(response.cats);
        // 同时构建 categoriesMap
        const map = buildCategoriesMap(response.cats);
        setCategoriesMap(map);
      }
    } catch (error) {
      message.error('Failed to load categories');
    } finally {
      setCategoriesLoading(false);
    }
  };

  // 根据分类加载属性
  const loadCategoryAttributes = async (id: number) => {
    // 检查是否已经加载过该分类的属性
    if (categoryAttributes.has(id)) {
      return;
    }

    try {
      const response = await getAttrList({
        globalCatId: id,
        region: getCountry(),
      });

      if (response?.data?.length) {
        setCategoryAttributes(prev => new Map(prev).set(id, response.data!));
      } else {
      }
    } catch (error) {
    }
  };

  // 加载免责声明数据
  const loadDisclaimerData = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      //
      // // 根据ID加载不同的模拟数据
      // if (id === '1') {
      //   // Alcohol Consumption
      //   setFormData({
      //     id: '1',
      //     name: 'Alcohol Consumption',
      //     description: 'The sale and consumption of alcoholic beverages are prohibited for minors. Drink responsibly.',
      //     status: true,
      //     categories: ['Cerveja e Cidra', 'Vinho e Champanhe', 'Licores e Destilados'],
      //     condition: {},
      //     legalDocument: {
      //       filename: 'alcohol_legal_approval.pdf',
      //       hash: 'alcohol_legal_approval_hash',
      //     },
      //   });
      // } else if (id === '2') {
      //   // Baby Formula Warning
      //   setFormData({
      //     id: '2',
      //     name: 'Baby Formula Warning',
      //     description: 'This product should only be administered under the supervision of a qualified healthcare professional. Breastfeeding is recommended up to 2 (two) years of age or beyond, as it is essential for the child\'s development. The information provided here does not replace personalized medical advice.',
      //     status: true,
      //     categories: [],
      //     advancedRules: [
      //       {
      //         id: 1,
      //         conditionId: 1,
      //         catId: 4, // Milk Formula category
      //         attrConditionInfo: [
      //           {
      //             id: 1,
      //             attrId: 1,
      //             operationType: OperationType.OPERATION_TYPE_EQUAL, // equals
      //             valueId: 4,
      //             value: '4', // ID for 'Growing-Up Milk (3+ years)'
      //             attributeName: 'Baby life Stage',
      //             attributeInputType: AttrInputType.SINGLE_DROP_DOWN,
      //             inputValidatorType: AttrInputValidatorType.VALIDATOR_STRING,
      //             operator: '=',
      //             valueType: 'id',
      //           },
      //         ],
      //       },
      //     ],
      //   });
      // }
    } catch (error) {
      message.error('Failed to load disclaimer data');
    } finally {
      setLoading(false);
    }
  };

  // 处理表单字段变化 - 现在由 Form 自动处理，不需要手动管理
  // 移除了 handleFieldChange 函数，因为 Form 会自动管理字段值

  // 获取所有已使用的分类ID（用于 Basic Settings）
  const getAllUsedCategoryIds = (): number[] => {
    return advancedRules
      .map(rule => rule.catId)
      .filter((catId): catId is number => catId !== undefined && catId !== null);
  };

  // 处理分类变化
  const handleCategoryChange = (categories: number[]) => {
    // 使用 categoriesMap 将 category ID 转换为 category 名称
    const categoryNames = categories.map(catId => {
      const category = categoriesMap.get(catId);
      return category?.catName || '';
    }).filter(name => name !== '');

    // 更新表单中的 categories 字段
    form.setFieldsValue({ categories: categoryNames });
  };

  // 添加高级规则
  const addAdvancedRule = () => {
    setRuleModalMode('add');
    setEditingRule(undefined);
    setRuleModalVisible(true);
  };

  // 编辑规则
  const editAdvancedRule = (rule: disclaimer.IUpdateDisclaimerConditionRequest) => {
    setRuleModalMode('edit');
    setEditingRule(rule);
    setRuleModalVisible(true);
  };

  // 处理规则Modal确认
  const handleRuleModalConfirm = (rule: disclaimer.IUpdateDisclaimerConditionRequest) => {
    if (ruleModalMode === 'add') {
      // 添加新规则
      const tempId = Date.now();
      const newRule = {
        ...rule,
        conditionId: tempId,
      };

      setAdvancedRules(prev => [...prev, newRule]);
      message.success('Rule added successfully');
    } else {
      // 编辑现有规则
      setAdvancedRules(prev =>
        prev.map(existingRule =>
          existingRule.conditionId === rule.conditionId ? rule : existingRule
        )
      );

      message.success('Rule updated successfully');
    }

    setRuleModalVisible(false);
    setEditingRule(undefined);
  };

  // 获取已使用的category IDs
  const getUsedCategoryIds = (): number[] => {
    return advancedRules.map(rule => rule.catId!).filter(Boolean);
  };

  // 删除高级规则
  const removeAdvancedRule = (conditionId: number, index: number) => {
    setAdvancedRules(prev => prev.filter(rule => rule.conditionId !== conditionId));
  };

  // 更新高级规则
  const updateAdvancedRule = (conditionId: number, index: number, updates: Partial<disclaimer.IUpdateDisclaimerConditionRequest>) => {
    setAdvancedRules(prev =>
      prev.map(rule => {
        if (rule.conditionId === conditionId) {
          // 如果更新的是分类，清空所有属性规则
          if (updates.catId && updates.catId !== rule.catId) {
            return {
              ...rule,
              ...updates,
              attrConditionInfo: {
                attrConditionList: [], // 清空属性规则
              },
            };
          }
          return { ...rule, ...updates };
        }
        return rule;
      })
    );
  };



  // 处理文件上传
  const handleFileUpload = async (data?: { hash?: string; filename?: string }) => {
    if (data) {
      const legalDocument = {
        filename: data.filename || '',
        hash: data.hash || '',
      };
      form.setFieldsValue({ legalDocument });
      message.success('File uploaded successfully');
    } else {
      form.setFieldsValue({ legalDocument: undefined });
      message.success('File removed successfully');
    }
  };

  // 校验属性规则是否完整
  const validateAttributeRules = (): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    advancedRules.forEach((rule, ruleIndex) => {
      if (rule.attrConditionInfo?.attrConditionList && rule.attrConditionInfo.attrConditionList.length > 0) {
        rule.attrConditionInfo.attrConditionList.forEach((attrRule, attrIndex) => {
          // 检查属性是否选择
          if (!attrRule.attrId) {
            errors.push(`Rule ${ ruleIndex + 1 }: Attribute ${ attrIndex + 1 } - Please select an attribute`);
          }

          // 检查操作符是否选择
          if (!attrRule.operationType) {
            errors.push(`Rule ${ ruleIndex + 1 }: Attribute ${ attrIndex + 1 } - Please select an operator`);
          }

          // 检查值是否填写
          if (!attrRule.value || !attrRule.value.trim()) {
            errors.push(`Rule ${ ruleIndex + 1 }: Attribute ${ attrIndex + 1 } - Please enter a value`);
          }
        });
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
    };
  };

  // 保存General Information
  const handleSaveGeneralInfo = async () => {
    try {
      // 使用 Form 验证基础字段
      await form.validateFields(['name', 'description']);

      setLoading(true);

      // 模拟API调用保存基本信息
      await new Promise(resolve => setTimeout(resolve, 1000));

      setGeneralInfoSaved(true);

      if (isEdit) {
        // 编辑模式：只保存，不跳转
        message.success('General information updated successfully.');
      } else {
        // 新增模式：保存并跳转到Advanced Settings
        setCurrentStep('advanced');
        message.success('General information saved successfully. You can now configure advanced settings.');
      }
    } catch (error) {
      if (error.errorFields) {
        // Form 验证错误
        message.error('Please check the form fields and try again.');
      } else {
        message.error('Failed to save general information');
      }
    } finally {
      setLoading(false);
    }
  };

  // 保存完整的免责声明（包括Advanced Settings）
  const handleSaveFinal = async () => {
    try {
      // 首先验证表单字段
      await form.validateFields();

      // 然后验证属性规则
      const validation = validateAttributeRules();
      if (!validation.isValid) {
        // 存储错误信息到状态中
        setValidationErrors(validation.errors);
        // 展开高级设置区域以显示错误
        setAdvancedSettingsExpanded(true);
        // 显示第一个错误信息
        message.error(`Validation failed: ${ validation.errors.length } error(s) found. Please check the Advanced Settings section.`);
        return;
      }

      // 清除之前的错误信息
      setValidationErrors([]);

      setLoading(true);

      // 获取表单数据
      const formValues = form.getFieldsValue();
      console.log('Form values:', formValues);
      console.log('Advanced rules:', advancedRules);

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      message.success(`Disclaimer ${ isEdit ? 'updated' : 'created' } successfully`);
      navigate('/product/disclaimers-management');
    } catch (error) {
      if (error.errorFields) {
        // Form 验证错误
        message.error('Please check the form fields and try again.');
      } else {
        message.error(`Failed to ${ isEdit ? 'update' : 'create' } disclaimer`);
      }
    } finally {
      setLoading(false);
    }
  };

  // 返回到General Information步骤
  const handleBackToGeneral = () => {
    setCurrentStep('general');
  };

  // 取消编辑
  const handleCancel = () => {
    navigate('/product/disclaimers-management');
  };

  // 生成规则摘要
  const generateRuleSummary = () => {
    const formValues = form.getFieldsValue();
    const { categories } = formValues;

    if (categories && categories.length > 0 && advancedRules.length === 0) {
      return `All items in: ${ categories.join(', ') } categories`;
    } else if (advancedRules.length > 0) {
      const summaries = advancedRules.map(rule => {
        const category = availableCategories.find(cat => cat.catId === rule.catId);
        const name = category?.catName || `Category ${rule.catId}`;
        if (!rule.attrConditionInfo?.attrConditionList || rule.attrConditionInfo.attrConditionList.length === 0) {
          return `Items in "${ name }" category`;
        } else {
          const attributeSummary = rule.attrConditionInfo.attrConditionList.map(ar => {
            const attr = categoryAttributes.get(rule.catId!)?.find(a => a.id === ar.attrId);
            const operatorName = DisclaimerOperationMap[ar.operationType as OperationType] || ar.operationType;
            return `${ attr?.name || ar.attrId } ${ operatorName } ${ ar.value }`;
          }).join(', ');
          return `Items in "${ name }" category where ${ attributeSummary }`;
        }
      });
      return summaries.join('; ');
    }

    return 'No rules configured';
  };

  return (
    <div className={ style.container }>
      {/* Header */ }
      <div className={ style.header }>
        <Title level={ 2 }>
          { isEdit ? 'Edit' : 'Add' } Disclaimer — { form.getFieldValue('name') || 'New Disclaimer' }
        </Title>
        <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
          {/* Step Indicator */}
          {!isEdit && (
            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              <div style={{
                padding: '4px 12px',
                borderRadius: '16px',
                backgroundColor: currentStep === 'general' ? '#1890ff' : '#52c41a',
                color: 'white',
                fontSize: '12px',
                fontWeight: 'bold'
              }}>
                Step 1: General Information
              </div>
              <div style={{
                width: '20px',
                height: '2px',
                backgroundColor: generalInfoSaved ? '#52c41a' : '#d9d9d9'
              }} />
              <div style={{
                padding: '4px 12px',
                borderRadius: '16px',
                backgroundColor: currentStep === 'advanced' ? '#1890ff' : '#d9d9d9',
                color: currentStep === 'advanced' ? 'white' : '#999',
                fontSize: '12px',
                fontWeight: 'bold'
              }}>
                Step 2: Advanced Settings
              </div>
            </div>
          )}
          <Button
            type="text"
            icon={ <CloseOutlined/> }
            onClick={ handleCancel }
            className={ style.closeButton }
          />
        </div>
      </div>

      {/* General Information - Step 1 */ }
      {(currentStep === 'general' || isEdit) && (
        <ConfigurableCard
          header={ {
            title: 'General Information',
            extra: (
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                {
                  <Button onClick={handleSaveGeneralInfo} loading={loading}>
                    Save
                  </Button>
                }
              </div>
            )
          } }
          className={ style.sectionCard }
        >
          <Form
            form={form}
            layout="vertical"
            initialValues={{
              name: '',
              description: '',
              status: true,
              legalDocument: undefined,
            }}
          >
            {/* Editable mode - General Information step or Edit mode */}
            {(currentStep === 'general' || isEdit) && (
              <>
                <FormField
                  label="Disclaimer Name"
                  name="name"
                  required
                  rules={validationRules.disclaimerName}
                >
                  <Input
                    placeholder="Enter disclaimer name"
                  />
                </FormField>

                <FormField
                  label="Disclaimer Description"
                  name="description"
                  required
                  rules={validationRules.disclaimerDescription}
                >
                  <TextArea
                    placeholder="Enter disclaimer description"
                    rows={ 4 }
                  />
                </FormField>

                <div className={ style.formField }>
                  <Text strong>Status</Text>
                  <div style={ { marginTop: 8 } }>
                    <Form.Item name="status" valuePropName="checked" style={{ marginBottom: 0 }}>
                      <div className={ style.statusToggle }>
                        <Switch />
                        <Text className={ style.statusText }>
                          {statusValue ? 'Active' : 'Inactive'}
                        </Text>
                      </div>
                    </Form.Item>
                  </div>
                </div>

                <FormField
                  label="Approval Document"
                  name="legalDocument"
                >
                  <UploadFile
                    file={ form.getFieldValue('legalDocument') || { hash: '', filename: '' } }
                    onChange={ handleFileUpload }
                    mode="edit"
                  />
                </FormField>
              </>
            )}

            {/* Read-only mode - Advanced Settings step */}
            {currentStep === 'advanced' && !isEdit && (
              <>
                <div className={ style.formField }>
                  <Text strong>Disclaimer Name</Text>
                  <div style={ { marginTop: 8 } }>
                    <div style={{
                      padding: '4px 11px',
                      border: '1px solid #d9d9d9',
                      borderRadius: '6px',
                      backgroundColor: '#f5f5f5',
                      minHeight: '32px',
                      display: 'flex',
                      alignItems: 'center'
                    }}>
                      <Text>{form.getFieldValue('name') || 'No name specified'}</Text>
                    </div>
                  </div>
                </div>

                <div className={ style.formField }>
                  <Text strong>Disclaimer Description</Text>
                  <div style={ { marginTop: 8 } }>
                    <div style={{
                      padding: '4px 11px',
                      border: '1px solid #d9d9d9',
                      borderRadius: '6px',
                      backgroundColor: '#f5f5f5',
                      minHeight: '80px',
                      display: 'flex',
                      alignItems: 'flex-start',
                      paddingTop: '8px'
                    }}>
                      <Text>{form.getFieldValue('description') || 'No description specified'}</Text>
                    </div>
                  </div>
                </div>

                <div className={ style.formField }>
                  <Text strong>Status</Text>
                  <div style={ { marginTop: 8 } }>
                    <div style={{
                      padding: '4px 11px',
                      border: '1px solid #d9d9d9',
                      borderRadius: '6px',
                      backgroundColor: '#f5f5f5',
                      minHeight: '32px',
                      display: 'flex',
                      alignItems: 'center'
                    }}>
                      <Text>
                        <span style={{
                          display: 'inline-block',
                          width: '8px',
                          height: '8px',
                          borderRadius: '50%',
                          backgroundColor: statusValue ? '#52c41a' : '#d9d9d9',
                          marginRight: '8px'
                        }}></span>
                        {statusValue ? 'Active' : 'Inactive'}
                      </Text>
                    </div>
                  </div>
                </div>

                <div className={ style.formField }>
                  <Text strong>Approval Document</Text>
                  <div style={ { marginTop: 8 } }>
                    <div style={{
                      padding: '4px 11px',
                      border: '1px solid #d9d9d9',
                      borderRadius: '6px',
                      backgroundColor: '#f5f5f5',
                      minHeight: '32px',
                      display: 'flex',
                      alignItems: 'center'
                    }}>
                      <Text>
                        {form.getFieldValue('legalDocument')?.filename || 'No document uploaded'}
                      </Text>
                    </div>
                  </div>
                </div>
              </>
            )}

            {/* Upload note - only show in editable mode */}
            {(currentStep === 'general' || isEdit) && (
              <div className={ style.uploadNote }>
                <Text type="secondary">This file is only for internal reference.</Text>
              </div>
            )}

            {/* Step 1 Actions - only show in General Information step */}
            {currentStep === 'general' && (
              <div style={{ marginTop: 24, display: 'flex', justifyContent: 'flex-end', gap: 8, paddingTop: 16, borderTop: '1px solid #f0f0f0' }}>
                <Button onClick={ handleCancel }>
                  Cancel
                </Button>
                <Button
                  type="primary"
                  loading={ loading }
                  onClick={ handleSaveGeneralInfo }
                >
                  {isEdit ? 'Save General Information' : 'Save & Continue to Advanced Settings'}
                </Button>
              </div>
            )}
          </Form>
        </ConfigurableCard>
      )}

      {/* Advanced Settings - Step 2 */ }
      {(
        <ConfigurableCard
          header={
            {
              title: 'Advanced Settings',
              icon: <SettingOutlined className={ style.sectionIcon }/>,
              description: <>Configure specific rules based on category + attribute conditions</>,
              extra: currentStep === 'advanced' && !isEdit && (
                <Button
                  type="link"
                  onClick={ handleBackToGeneral }
                  style={{ padding: 0, fontSize: '12px' }}
                >
                  ← Back to General Information
                </Button>
              ),
            }
          }
          collapsible={ true }
          defaultCollapsed={ false }
          className={ style.sectionCard }
          onCollapse={ (collapsed) => setAdvancedSettingsExpanded(!collapsed) }
        >
        <div className={ style.advancedContent }>
          {/* 校验错误提示 */ }
          { validationErrors.length > 0 && (
            <div className={ style.validationErrors }>
              <Text type="danger" strong>Validation Errors:</Text>
              <ul className={ style.errorList }>
                { validationErrors.map((error, index) => (
                  <li key={ index } className={ style.errorItem }>
                    <Text type="danger">{ error }</Text>
                  </li>
                )) }
              </ul>
            </div>
          ) }

          { advancedRules.length === 0 ? (
            <div className={ style.noRules }>
              <Text type="secondary">No advanced rules configured</Text>
            </div>
          ) : (
            advancedRules.map((rule, index) => (
              <RuleCard
                key={ rule.conditionId }
                rule={ rule }
                index={ index }
                allRules={ advancedRules }
                availableCategories={ availableCategories }
                categoriesMap={ categoriesMap }
                categoryAttributes={ categoryAttributes }
                categoriesLoading={ categoriesLoading }
                onUpdate={ updateAdvancedRule }
                onDelete={ removeAdvancedRule }
                onCategoryChange={ loadCategoryAttributes }
                onEditRule={ editAdvancedRule }
              />
            ))
          ) }

          <Button
            type="primary"
            icon={ <PlusOutlined/> }
            onClick={ addAdvancedRule }
            style={ { marginTop: 16 } }
          >
            Add Another Rule
          </Button>
        </div>
      </ConfigurableCard>
      )}

      {/* Rule Summary - Only show in Advanced Settings step */ }
      <ConfigurableCard
        header={ {
          title: <div style={ { textAlign: 'center' } }>
            Rule Summary
          </div>,
        } }
        className={ style.sectionCard }
      >
        <div className={ style.ruleSummary }>
          <Text className={ style.ruleSummaryTitle }>This disclaimer will be applied to:</Text>
          <div className={ style.ruleSummaryContent }>
            • { generateRuleSummary() }
          </div>
        </div>
      </ConfigurableCard>

      {/* Final Actions */}
      {(currentStep === 'advanced' || isEdit) && (
        <div style={{
          display: 'flex',
          justifyContent: 'flex-end',
          gap: 8,
          padding: '24px',
          background: 'white',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
          marginTop: '24px'
        }}>
          <Button onClick={handleCancel}>
            Cancel
          </Button>
          <Button
            type="primary"
            loading={loading}
            onClick={handleSaveFinal}
          >
            {isEdit ? 'Update Disclaimer' : 'Create Disclaimer'}
          </Button>
        </div>
      )}

      {/* Rule Modal */}
      <RuleModal
        visible={ruleModalVisible}
        onCancel={() => {
          setRuleModalVisible(false);
          setEditingRule(undefined);
        }}
        onConfirm={handleRuleModalConfirm}
        availableCategories={availableCategories}
        categoryAttributes={categoryAttributes}
        categoriesLoading={categoriesLoading}
        usedCategoryIds={getUsedCategoryIds()}
        onCategoryChange={loadCategoryAttributes}
        editingRule={editingRule}
        mode={ruleModalMode}
      />
    </div>
  );
};

export default DisclaimerEditOrAdd;

