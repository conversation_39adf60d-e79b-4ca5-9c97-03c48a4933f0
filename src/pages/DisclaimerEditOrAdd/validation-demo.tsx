import React from 'react';
import { Form, Input, Button, message } from 'antd';

const { TextArea } = Input;

// 演示验证规则
const demoValidationRules = {
  disclaimerName: [
    { required: true, message: 'Please enter disclaimer name' },
    { max: 100, message: 'Disclaimer name cannot exceed 100 characters' },
    { whitespace: true, message: 'Disclaimer name cannot be empty' },
  ],
  disclaimerDescription: [
    { required: true, message: 'Please enter disclaimer description' },
    { max: 1000, message: 'Description cannot exceed 1000 characters' },
    { whitespace: true, message: 'Description cannot be empty' },
  ],
};

const ValidationDemo: React.FC = () => {
  const [form] = Form.useForm();

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      console.log('Form values:', values);
      message.success('Validation passed! Form submitted successfully.');
    } catch (error) {
      console.log('Validation failed:', error);
      message.error('Please check the form fields and try again.');
    }
  };

  const handleReset = () => {
    form.resetFields();
  };

  return (
    <div style={{ padding: '24px', maxWidth: '600px' }}>
      <h2>Form Validation Demo</h2>
      
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          name: '',
          description: '',
        }}
      >
        <Form.Item
          label="Disclaimer Name"
          name="name"
          rules={demoValidationRules.disclaimerName}
        >
          <Input placeholder="Enter disclaimer name" />
        </Form.Item>

        <Form.Item
          label="Disclaimer Description"
          name="description"
          rules={demoValidationRules.disclaimerDescription}
        >
          <TextArea
            placeholder="Enter disclaimer description"
            rows={4}
          />
        </Form.Item>

        <Form.Item>
          <div style={{ display: 'flex', gap: '8px' }}>
            <Button type="primary" onClick={handleSubmit}>
              Submit (Validate)
            </Button>
            <Button onClick={handleReset}>
              Reset
            </Button>
          </div>
        </Form.Item>
      </Form>

      <div style={{ marginTop: '24px', padding: '16px', backgroundColor: '#f5f5f5', borderRadius: '6px' }}>
        <h3>Test Cases:</h3>
        <ul>
          <li>Leave fields empty and click Submit - should show required field errors</li>
          <li>Enter only spaces - should show whitespace validation errors</li>
          <li>Enter text longer than limits - should show length validation errors</li>
          <li>Fill correctly - should pass validation and submit</li>
        </ul>
      </div>
    </div>
  );
};

export default ValidationDemo;
