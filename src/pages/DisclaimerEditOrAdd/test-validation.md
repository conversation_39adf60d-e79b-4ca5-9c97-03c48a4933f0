# Form Validation Test Guide

## 验证规则测试

### 1. Disclaimer Name 字段验证
- **必填验证**: 留空提交，应显示 "Please enter disclaimer name"
- **长度验证**: 输入超过100个字符，应显示 "Disclaimer name cannot exceed 100 characters"
- **空白验证**: 只输入空格，应显示 "Disclaimer name cannot be empty"

### 2. Disclaimer Description 字段验证
- **必填验证**: 留空提交，应显示 "Please enter disclaimer description"
- **长度验证**: 输入超过1000个字符，应显示 "Description cannot exceed 1000 characters"
- **空白验证**: 只输入空格，应显示 "Description cannot be empty"

### 3. Status 字段
- 默认值应为 true (Active)
- 切换开关应正确更新显示文本

### 4. 表单提交验证
- 点击 "Save" 按钮时，应触发表单验证
- 如果有验证错误，应阻止提交并显示错误信息
- 验证通过后，应正常提交

## 测试步骤

1. 打开 DisclaimerEditOrAdd 页面
2. 尝试直接点击 "Save" 按钮 - 应该显示必填字段错误
3. 输入超长文本测试长度限制
4. 输入只有空格的文本测试空白验证
5. 正确填写所有字段后提交 - 应该成功

## 预期行为

- 表单验证应在提交时触发
- 错误信息应清晰显示在对应字段下方
- 验证通过后应能正常保存并跳转到下一步
