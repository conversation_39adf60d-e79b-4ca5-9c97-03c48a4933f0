import React from 'react';
import { Typography } from 'antd';
import type { FormInstance } from 'antd/lib/form';

import type { OperationType } from 'src/api/disclaimer/constants';
import type { disclaimer } from 'src/api/disclaimer/disclaimer';
import type { globalAttribute } from 'src/api/globalAttribute/globalAttribute';
import type { uploadAdmin } from 'src/api/uploadAdmin/uploadAdmin';
import ConfigurableCard from 'src/components/ConfigurableCard';
import { DisclaimerOperationMap } from 'src/constants/disclaimer';
import style from '../style.module.scss';

const { Text } = Typography;

interface RuleSummaryProps {
  form: FormInstance;
  advancedRules: disclaimer.IUpdateDisclaimerConditionRequest[];
  availableCategories: uploadAdmin.ICategory[];
  categoryAttributes: Map<number, globalAttribute.IGlobalAttr[]>;
}

const RuleSummary: React.FC<RuleSummaryProps> = ({
  form,
  advancedRules,
  availableCategories,
  categoryAttributes,
}) => {
  // 生成规则摘要
  const generateRuleSummary = () => {
    const formValues = form.getFieldsValue();
    const { categories } = formValues;

    if (categories && categories.length > 0 && advancedRules.length === 0) {
      return `All items in: ${categories.join(', ')} categories`;
    } else if (advancedRules.length > 0) {
      const summaries = advancedRules.map(rule => {
        const category = availableCategories.find(cat => cat.catId === rule.catId);
        const name = category?.catName || `Category ${rule.catId}`;
        if (!rule.attrConditionInfo?.attrConditionList || rule.attrConditionInfo.attrConditionList.length === 0) {
          return `Items in "${name}" category`;
        } else {
          const attributeSummary = rule.attrConditionInfo.attrConditionList.map(ar => {
            const attr = categoryAttributes.get(rule.catId!)?.find(a => a.id === ar.attrId);
            const operatorName = DisclaimerOperationMap[ar.operationType as OperationType] || ar.operationType;
            return `${attr?.name || ar.attrId} ${operatorName} ${ar.value}`;
          }).join(', ');
          return `Items in "${name}" category where ${attributeSummary}`;
        }
      });
      return summaries.join('; ');
    }

    return 'No rules configured';
  };

  return (
    <ConfigurableCard
      header={{
        title: <div style={{ textAlign: 'center' }}>
          Rule Summary
        </div>,
      }}
      className={style.sectionCard}
    >
      <div className={style.ruleSummary}>
        <Text className={style.ruleSummaryTitle}>This disclaimer will be applied to:</Text>
        <div className={style.ruleSummaryContent}>
          • {generateRuleSummary()}
        </div>
      </div>
    </ConfigurableCard>
  );
};

export default RuleSummary;
