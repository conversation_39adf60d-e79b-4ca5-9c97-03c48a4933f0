import {
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import {
  Button,
  Popconfirm,
  Table,
  Typography,
} from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import React, {
  useState,
} from 'react';

import { OperationType } from 'src/api/disclaimer/constants';
import type {
  disclaimer,
} from 'src/api/disclaimer/disclaimer';
import type {
  globalAttribute,
} from 'src/api/globalAttribute/globalAttribute';
import { DisclaimerOperationMap } from 'src/constants/disclaimer';
import AttributeRuleModal from './AttributeRuleModal';

const { Text } = Typography;

interface AttributeRuleTableProps {
  attributeRules: disclaimer.IAttrCondition[];
  categoryAttributes: globalAttribute.IGlobalAttr[];
  onAdd: (attributeRule: disclaimer.IAttrCondition) => void;
  onEdit: (index: number, attributeRule: disclaimer.IAttrCondition) => void;
  onDelete: (index: number) => void;
}

interface AttributeRuleWithIndex extends disclaimer.IAttrCondition {
  index: number;
}

const AttributeRuleTable: React.FC<AttributeRuleTableProps> = ({
  attributeRules,
  categoryAttributes,
  onAdd,
  onEdit,
  onDelete,
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [editingRule, setEditingRule] = useState<disclaimer.IAttrCondition | undefined>();

  // 获取已使用的属性ID列表
  const getUsedAttributeIds = (excludeIndex?: number): number[] => {
    return attributeRules
      .map((rule, index) => index !== excludeIndex ? rule.attrId : null)
      .filter((id): id is number => id !== null);
  };

  // 获取属性名称
  const getAttributeName = (attrId: number): string => {
    const attr = categoryAttributes.find(a => a.id === attrId);
    return attr?.name || `Attribute ${attrId}`;
  };

  // 获取操作符显示名称
  const getOperatorName = (operationType: number): string => {
    return DisclaimerOperationMap[operationType as OperationType] || `Operation ${operationType}`;
  };

  // 处理添加
  const handleAdd = () => {
    setEditingIndex(null);
    setEditingRule(undefined);
    setModalVisible(true);
  };

  // 处理编辑
  const handleEdit = (index: number) => {
    setEditingIndex(index);
    setEditingRule(attributeRules[index]);
    setModalVisible(true);
  };

  // 处理删除
  const handleDelete = (index: number) => {
    onDelete(index);
  };

  // 处理模态框确认
  const handleModalOk = (attributeRule: disclaimer.IAttrCondition) => {
    if (editingIndex !== null) {
      onEdit(editingIndex, attributeRule);
    } else {
      onAdd(attributeRule);
    }
    setModalVisible(false);
    setEditingIndex(null);
    setEditingRule(undefined);
  };

  // 处理模态框取消
  const handleModalCancel = () => {
    setModalVisible(false);
    setEditingIndex(null);
    setEditingRule(undefined);
  };

  // 表格数据源
  const dataSource: AttributeRuleWithIndex[] = attributeRules.map((rule, index) => ({
    ...rule,
    index,
  }));

  // 表格列定义
  const columns: ColumnsType<AttributeRuleWithIndex> = [
    {
      title: 'Attribute',
      dataIndex: 'attrId',
      key: 'attrId',
      width: '30%',
      render: (attrId: number) => (
        <Text>{getAttributeName(attrId)}</Text>
      ),
    },
    {
      title: 'Operator',
      dataIndex: 'operationType',
      key: 'operationType',
      width: '20%',
      render: (operationType: number) => (
        <Text>{getOperatorName(operationType)}</Text>
      ),
    },
    {
      title: 'Value',
      dataIndex: 'value',
      key: 'value',
      width: '30%',
      render: (value: string) => (
        <Text>{value}</Text>
      ),
    },
    {
      title: 'Action',
      key: 'action',
      width: '20%',
      render: (_, record) => (
        <div style={{ display: 'flex', gap: 8 }}>
          <Button
            type="text"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record.index)}
            title="Edit"
          />
          <Popconfirm
            title="Delete this attribute rule?"
            onConfirm={() => handleDelete(record.index)}
            okText="Yes"
            cancelText="No"
          >
            <Button
              type="text"
              size="small"
              danger
              icon={<DeleteOutlined />}
              title="Delete"
            />
          </Popconfirm>
        </div>
      ),
    },
  ];

  return (
    <div>
      <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Text strong>Attribute Rules</Text>
        <Button
          type="primary"
          size="small"
          icon={<PlusOutlined />}
          onClick={handleAdd}
          disabled={categoryAttributes.length === 0}
        >
          Add Attribute Rule
        </Button>
      </div>

      <Table
        dataSource={dataSource}
        columns={columns}
        pagination={false}
        size="small"
        rowKey="index"
        locale={{
          emptyText: 'No attribute rules configured',
        }}
        style={{ marginBottom: 16 }}
      />

      <AttributeRuleModal
        visible={modalVisible}
        onCancel={handleModalCancel}
        onOk={handleModalOk}
        categoryAttributes={categoryAttributes}
        usedAttributeIds={getUsedAttributeIds(editingIndex || undefined)}
        editingRule={editingRule}
        title={editingIndex !== null ? 'Edit Attribute Rule' : 'Add Attribute Rule'}
      />
    </div>
  );
};

export default AttributeRuleTable;
