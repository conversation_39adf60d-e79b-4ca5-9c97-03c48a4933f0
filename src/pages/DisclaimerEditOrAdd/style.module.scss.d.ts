declare const classNames: {
  readonly container: "container";
  readonly header: "header";
  readonly closeButton: "closeButton";
  readonly sectionCard: "sectionCard";
  readonly antCardHead: "antCardHead";
  readonly antCardHeadTitle: "antCardHeadTitle";
  readonly sectionTitle: "sectionTitle";
  readonly sectionIcon: "sectionIcon";
  readonly sectionDescription: "sectionDescription";
  readonly formField: "formField";
  readonly statusToggle: "statusToggle";
  readonly statusText: "statusText";
  readonly advancedContent: "advancedContent";
  readonly validationErrors: "validationErrors";
  readonly errorList: "errorList";
  readonly errorItem: "errorItem";
  readonly ruleCard: "ruleCard";
  readonly ruleHeader: "ruleHeader";
  readonly deleteButton: "deleteButton";
  readonly ruleContent: "ruleContent";
  readonly categorySelect: "categorySelect";
  readonly attributeRule: "attributeRule";
  readonly attributeRuleHeader: "attributeRuleHeader";
  readonly noRules: "noRules";
  readonly ruleSummary: "ruleSummary";
  readonly ruleSummaryTitle: "ruleSummaryTitle";
  readonly antTypography: "antTypography";
  readonly ruleSummaryContent: "ruleSummaryContent";
  readonly documentUploaded: "documentUploaded";
  readonly documentInfo: "documentInfo";
  readonly checkIcon: "checkIcon";
  readonly documentName: "documentName";
  readonly uploadArea: "uploadArea";
  readonly uploadIcon: "uploadIcon";
  readonly uploadNote: "uploadNote";
  readonly footer: "footer";
  readonly antRow: "antRow";
  readonly antCol: "antCol";
};
export = classNames;
