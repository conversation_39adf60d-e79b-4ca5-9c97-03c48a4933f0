# 两步骤创建流程 - General Information + Advanced Settings

## 概述

现在的Disclaimer创建流程分为两个清晰的步骤：
1. **Step 1**: General Information - 创建基本信息
2. **Step 2**: Advanced Settings - 配置高级规则

## 用户流程

### 🎯 **新增Disclaimer流程**

#### Step 1: General Information
1. 用户填写基本信息：
   - Disclaimer Name (必填)
   - Disclaimer Description (必填)
   - Status (Active/Inactive)
   - Approval Document (可选)

2. 点击 **"Save & Continue to Advanced Settings"** 按钮
   - 验证必填字段
   - 保存General Information到后端
   - 自动跳转到Step 2

#### Step 2: Advanced Settings
1. General Information显示为只读模式，带有 **"Edit"** 按钮
2. 用户可以配置Advanced Settings：
   - 添加规则 (Add Another Rule)
   - 编辑现有规则
   - 删除规则

3. 点击 **"Create Disclaimer"** 按钮完成创建

### 🎯 **编辑Disclaimer流程**

#### 编辑模式特点
- 直接显示所有内容（General Information + Advanced Settings）
- General Information可编辑，有独立的保存按钮
- Advanced Settings正常可编辑

## 界面变化

### Step 1 - General Information
```typescript
// 新增模式 - 可编辑表单
<FormField label="Disclaimer Name" required>
  <Input
    placeholder="Enter disclaimer name"
    value={formData.name}
    onChange={(e) => handleFieldChange('name', e.target.value)}
  />
</FormField>

// 保存按钮
<Button type="primary" onClick={handleSaveGeneralInfo}>
  Save & Continue to Advanced Settings
</Button>
```

### Step 2 - Advanced Settings
```typescript
// General Information - 只读显示
<FormField label="Disclaimer Name">
  <div style={{ 
    padding: '4px 11px', 
    backgroundColor: '#f5f5f5',
    border: '1px solid #d9d9d9',
    borderRadius: '6px'
  }}>
    <Text>{formData.name}</Text>
  </div>
</FormField>

// 编辑按钮
<Button size="small" onClick={() => setCurrentStep('general')}>
  Edit
</Button>
```

## 技术实现

### 状态管理
```typescript
const [currentStep, setCurrentStep] = useState<'general' | 'advanced'>(
  isEdit ? 'advanced' : 'general'
);
const [generalInfoSaved, setGeneralInfoSaved] = useState(isEdit);
```

### 步骤指示器
```typescript
// Header中的步骤指示器
{!isEdit && (
  <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
    <div style={{
      backgroundColor: currentStep === 'general' ? '#1890ff' : '#52c41a',
      color: 'white'
    }}>
      Step 1: General Information
    </div>
    <div style={{
      backgroundColor: currentStep === 'advanced' ? '#1890ff' : '#d9d9d9',
      color: currentStep === 'advanced' ? 'white' : '#999'
    }}>
      Step 2: Advanced Settings
    </div>
  </div>
)}
```

### 条件渲染
```typescript
// General Information - 根据步骤显示不同模式
{(currentStep === 'general' || isEdit) && (
  <ConfigurableCard>
    {/* 可编辑模式 */}
    {(currentStep === 'general' || isEdit) && (
      <Input value={formData.name} onChange={...} />
    )}
    
    {/* 只读模式 */}
    {currentStep === 'advanced' && !isEdit && (
      <div style={{ backgroundColor: '#f5f5f5' }}>
        <Text>{formData.name}</Text>
      </div>
    )}
  </ConfigurableCard>
)}

// Advanced Settings - 只在Step 2显示
{(currentStep === 'advanced' || isEdit) && (
  <ConfigurableCard>
    {/* Advanced Settings内容 */}
  </ConfigurableCard>
)}
```

## 保存逻辑

### General Information保存
```typescript
const handleSaveGeneralInfo = async () => {
  // 验证必填字段
  if (!formData.name?.trim()) {
    message.error('Please enter disclaimer name');
    return;
  }

  // 保存到后端
  await saveGeneralInfo(formData);
  
  setGeneralInfoSaved(true);
  
  if (isEdit) {
    // 编辑模式：只保存，不跳转
    message.success('General information updated successfully.');
  } else {
    // 新增模式：保存并跳转
    setCurrentStep('advanced');
    message.success('General information saved successfully.');
  }
};
```

### 最终保存
```typescript
const handleSaveFinal = async () => {
  // 验证Advanced Settings
  const validation = validateAttributeRules();
  if (!validation.isValid) {
    message.error('Please fix validation errors');
    return;
  }

  // 保存完整的disclaimer
  await saveDisclaimer(formData);
  
  message.success(`Disclaimer ${isEdit ? 'updated' : 'created'} successfully`);
  navigate('/product/disclaimers-management');
};
```

## 用户体验优势

### 🎯 **清晰的流程**
- 分步骤引导用户完成创建
- 每个步骤专注于特定的任务
- 明确的进度指示

### 🎯 **灵活的编辑**
- 可以随时返回编辑General Information
- 保存状态清晰可见
- 支持部分保存

### 🎯 **数据安全**
- General Information先保存，避免数据丢失
- 分步验证，减少错误
- 清晰的保存状态反馈

### 🎯 **向后兼容**
- 编辑模式保持原有体验
- 所有现有功能正常工作
- 平滑的用户体验过渡

## 最佳实践

### 🎯 **新用户**
建议按步骤完成：先保存General Information，再配置Advanced Settings

### 🎯 **经验用户**
可以在Advanced Settings步骤快速编辑General Information

### 🎯 **数据完整性**
确保General Information保存后再进行复杂的规则配置
