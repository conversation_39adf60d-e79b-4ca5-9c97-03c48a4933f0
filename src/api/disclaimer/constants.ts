export enum ErrorCode {
  /** general error code => [1726900000, 1727001000) */
  /** invalid request param */
  ERROR_PARAM = 1726900000,
  /** database general error */
  ERROR_DATABASE = 1726900001,
  /** internal program mistake */
  ERROR_INTERNAL = 1726900002,
  /** json/proto marshal error */
  ERROR_MARSHAL = 1726900003,
  /** json/proto unmarshal error */
  ERROR_UNMARSHAL = 1726900004,
}

export enum DisclaimerStatus {
  DISCLAIMER_NORMAL = 1,
  DISCLAIMER_DISABLED = 2,
  DISCLAIMER_DELETED = 3,
  DISCLAIMER_INVALID = 4,
}

export enum DisclaimerCondStatus {
  DISCLAIMER_COND_NORMAL = 1,
  DISCLAIMER_COND_DISABLED = 2,
  DISCLAIMER_COND_DELETED = 3,
}

export enum OperationType {
  /** = */
  OPERATION_TYPE_EQUAL = 1,
  /** != */
  OPERATION_TYPE_NOT_EQUAL = 2,
  /** > */
  OPERATION_TYPE_GREATER = 3,
  /** < */
  OPERATION_TYPE_LESS = 4,
  /** >= */
  OPERATION_TYPE_GREATER_EQUAL = 5,
  /** <= */
  OPERATION_TYPE_LESS_EQUAL = 6,
}

