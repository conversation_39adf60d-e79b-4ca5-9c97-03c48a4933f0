import { disclaimer } from './disclaimer';
import { api<PERSON><PERSON><PERSON>, WithResponse } from 'src/api/helper/apiHandler';
import { request } from 'src/api/helper';

const URLPrefix = '/api/v2/classification/disclaimer';

function createRequestFunc<IRequest, IResponse>(url: string) {
  return function (
    requestBody: IRequest,
    config?: { returnFullResponse?: boolean; skipError?: boolean }
  ) {
    return apiHandler(
      request<WithResponse<IResponse>>({
        url: URLPrefix + url,
        data: requestBody,
      }),
      config
    );
  };
}

export const addDisclaimer = createRequestFunc<
  disclaimer.IAddDisclaimerRequest,
  disclaimer.IAddDisclaimerResponse
>('/add_disclaimer');

export const updateDisclaimer = createRequestFunc<
  disclaimer.IUpdateDisclaimerRequest,
  disclaimer.IUpdateDisclaimerResponse
>('/update_disclaimer');

export const deleteDisclaimer = createRequestFunc<
  disclaimer.IDeleteDisclaimerRequest,
  disclaimer.IDeleteDisclaimerResponse
>('/delete_disclaimer');

export const getDisclaimerWithCond = createRequestFunc<
  disclaimer.IGetDisclaimerWithCondRequest,
  disclaimer.IGetDisclaimerWithCondResponse
>('/get_disclaimer_with_cond');

export const getDisclaimersWithPaging = createRequestFunc<
  disclaimer.IGetDisclaimersWithPagingRequest,
  disclaimer.IGetDisclaimersWithPagingResponse
>('/get_disclaimers_with_paging');

export const exportDisclaimersWithPaging = createRequestFunc<
  disclaimer.IExportDisclaimersWithPagingRequest,
  disclaimer.IExportDisclaimersWithPagingResponse
>('/export_disclaimers_with_paging');

export const addDisclaimerCondition = createRequestFunc<
  disclaimer.IAddDisclaimerConditionRequest,
  disclaimer.IAddDisclaimerConditionResponse
>('/add_disclaimer_condition');

export const updateDisclaimerCondition = createRequestFunc<
  disclaimer.IUpdateDisclaimerConditionRequest,
  disclaimer.IUpdateDisclaimerConditionResponse
>('/update_disclaimer_condition');

export const deleteDisclaimerCondition = createRequestFunc<
  disclaimer.IDeleteDisclaimerConditionRequest,
  disclaimer.IDeleteDisclaimerConditionResponse
>('/delete_disclaimer_condition');
