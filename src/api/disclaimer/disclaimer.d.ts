export namespace disclaimer {
  // proto syntax: proto2  

  // proto package name: disclaimer  

  export interface IConstant {
    /** error code range => [1726900000, 1727000000) */
  }

  export interface IDisclaimerWithCond {
    disclaimer?: IDisclaimer;
    conditionInfo?: IDisclaimerConditions;
  }

  export interface IDisclaimer {
    id?: number;
    name?: string;
    description?: string;
    displayDescription?: IMultiLangList;
    approvalFile?: IFileInfo;
    /** ref: DisclaimerStatus */
    disclaimerStatus?: number;
    operator?: string;
    ctime?: number;
    mtime?: number;
  }

  export interface IDisclaimerConditions {
    disclaimerConditionList?: IDisclaimerCondition[];
  }

  export interface IDisclaimerCondition {
    id?: number;
    disclaimerId?: number;
    catId?: number;
    attrConditionInfo?: IAttrConditions;
    /** ref: DisclaimerCondStatus */
    condStatus?: number;
    operator?: string;
    ctime?: number;
    mtime?: number;
  }

  export interface IAttrConditions {
    attrConditionList?: IAttrCondition[];
  }

  export interface IAttrCondition {
    /** ref: OperationType */
    operationType?: number;
    attrId?: number;
    valueId?: number;
    value?: string;
  }

  export interface IMultiLangList {
    multiLangValues?: IValWithLang[];
  }

  export interface IValWithLang {
    lang?: string;
    value?: string;
  }

  export interface IFileInfo {
    fileName?: string;
    fileAddr?: string;
  }

  export interface IAddDisclaimerRequest {
    /** mandatory */
    operator?: string;
    /** mandatory */
    name?: string;
    /** mandatory */
    description?: string;
    /** optional */
    displayDescription?: IMultiLangList;
    /** optional */
    approvalFile?: IFileInfo;
  }

  export interface IAddDisclaimerResponse {
    debugMsg?: string;
  }

  export interface IUpdateDisclaimerRequest {
    /** mandatory */
    operator?: string;
    /** mandatory */
    disclaimerId?: number;
    /** optional */
    name?: string;
    /** optional */
    description?: string;
    /** optional */
    displayDescription?: IMultiLangList;
    /** optional */
    approvalFile?: IFileInfo;
    /** optional */
    disclaimerStatus?: number;
  }

  export interface IUpdateDisclaimerResponse {
    debugMsg?: string;
  }

  export interface IDeleteDisclaimerRequest {
    /** mandatory */
    operator?: string;
    /** mandatory */
    disclaimerId?: number;
  }

  export interface IDeleteDisclaimerResponse {
    debugMsg?: string;
  }

  export interface IGetDisclaimerWithCondRequest {
    /** mandatory */
    disclaimerId?: number;
  }

  export interface IGetDisclaimerWithCondResponse {
    debugMsg?: string;
    disclaimerWithCond?: IDisclaimerWithCond;
  }

  export interface IGetDisclaimersWithPagingRequest {
    /** mandatory */
    offset?: number;
    /** mandatory, max limit = 100 */
    limit?: number;
    /** optional */
    disclaimerName?: string;
  }

  export interface IGetDisclaimersWithPagingResponse {
    debugMsg?: string;
    hasNext?: boolean;
    nextOffset?: number;
    disclaimers?: IDisclaimer[];
  }

  export interface IExportDisclaimersWithPagingRequest {
    /** mandatory */
    offset?: number;
    /** mandatory, max limit = 100 */
    limit?: number;
    /** optional */
    disclaimerName?: string;
  }

  export interface IExportDisclaimersWithPagingResponse {
    debugMsg?: string;
    hasNext?: boolean;
    nextOffset?: number;
    disclaimers?: IDisclaimer[];
  }

  export interface IAddDisclaimerConditionRequest {
    /** mandatory */
    operator?: string;
    /** mandatory */
    disclaimerId?: number;
    /** mandatory */
    catId?: number;
    /** optional */
    attrConditionInfo?: IAttrConditions;
  }

  export interface IAddDisclaimerConditionResponse {
    debugMsg?: string;
  }

  export interface IUpdateDisclaimerConditionRequest {
    /** mandatory */
    operator?: string;
    /** mandatory */
    conditionId?: number;
    /** optional */
    catId?: number;
    /** optional */
    attrConditionInfo?: IAttrConditions;
    /** optional, ref: DisclaimerCondStatus */
    condStatus?: number;
  }

  export interface IUpdateDisclaimerConditionResponse {
    debugMsg?: string;
  }

  export interface IDeleteDisclaimerConditionRequest {
    /** mandatory */
    operator?: string;
    /** mandatory */
    conditionId?: number;
  }

  export interface IDeleteDisclaimerConditionResponse {
    debugMsg?: string;
  }

}
